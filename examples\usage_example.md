# ONNX Service 扩展使用示例

## 1. 准备模型配置文件

为您的train.py模型创建配置文件 `models/train_model_config.json`：

```json
{
  "model_info": {
    "name": "multi_output_regression",
    "version": "1.0.0",
    "description": "多输出回归模型，基于RandomForest实现，2个特征输入，3个目标输出",
    "author": "ML Team",
    "created_at": "2024-01-15T10:30:00Z",
    "tags": ["regression", "multi-output", "random-forest"]
  },
  "runtime": {
    "max_batch_size": 32,
    "timeout_ms": 5000,
    "optimization_level": 3
  },
  "inputs": [
    {
      "name": "float_input",
      "dtype": "float32",
      "shape": [-1, 2],
      "description": "两个特征的输入数据，支持批处理",
      "example": [[1.5, 2.3], [0.8, 1.9]],
      "validation": {
        "min_value": -10.0,
        "max_value": 10.0,
        "required": true
      }
    }
  ],
  "outputs": [
    {
      "name": "variable_0",
      "dtype": "float32", 
      "shape": [-1, 1],
      "description": "第一个回归目标的预测值",
      "interpretation": {
        "type": "regression",
        "unit": "numeric"
      }
    },
    {
      "name": "variable_1", 
      "dtype": "float32",
      "shape": [-1, 1],
      "description": "第二个回归目标的预测值",
      "interpretation": {
        "type": "regression", 
        "unit": "numeric"
      }
    },
    {
      "name": "variable_2",
      "dtype": "float32",
      "shape": [-1, 1], 
      "description": "第三个回归目标的预测值",
      "interpretation": {
        "type": "regression",
        "unit": "numeric"
      }
    }
  ],
  "api": {
    "endpoint_prefix": "/models",
    "enable_batch": true
  }
}
```

## 2. 注册模型

使用新的注册接口注册模型：

```bash
curl -X POST http://localhost:8080/models/register \
  -H "Content-Type: application/json" \
  -d '{
    "model_path": "./models/model.onnx",
    "config_path": "./models/train_model_config.json"
  }'
```

响应示例：
```json
{
  "success": true,
  "message": "Model multi_output_regression:1.0.0 registered successfully",
  "model_id": "multi_output_regression:1.0.0",
  "endpoints": [
    "/models/multi_output_regression/info",
    "/models/multi_output_regression/predict",
    "/models/multi_output_regression/batch_predict"
  ]
}
```

## 3. 查看已注册的模型

```bash
curl http://localhost:8080/models
```

响应示例：
```json
{
  "total": 1,
  "models": [
    {
      "model_id": "multi_output_regression:1.0.0",
      "name": "multi_output_regression",
      "version": "1.0.0",
      "description": "多输出回归模型，基于RandomForest实现，2个特征输入，3个目标输出",
      "status": "active",
      "endpoints": [
        "/models/multi_output_regression/info",
        "/models/multi_output_regression/predict",
        "/models/multi_output_regression/batch_predict"
      ]
    }
  ]
}
```

## 4. 使用标准化预测接口

### 单次预测
```bash
curl -X POST http://localhost:8080/models/multi_output_regression/predict \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "float_input": [[1.5, 2.3]]
    },
    "options": {
      "verbose": true
    }
  }'
```

响应示例：
```json
{
  "predictions": {
    "variable_0": [1.234],
    "variable_1": [2.567],
    "variable_2": [0.891]
  },
  "model_info": {
    "name": "multi_output_regression",
    "version": "1.0.0",
    "description": "多输出回归模型，基于RandomForest实现，2个特征输入，3个目标输出"
  },
  "inference_time_ms": 15.6,
  "request_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 批量预测
```bash
curl -X POST http://localhost:8080/models/multi_output_regression/batch_predict \
  -H "Content-Type: application/json" \
  -d '{
    "batch_inputs": [
      {"float_input": [[1.5, 2.3]]},
      {"float_input": [[0.8, 1.9]]},
      {"float_input": [[2.1, 3.4]]}
    ]
  }'
```

## 5. 获取模型详细信息

```bash
curl http://localhost:8080/models/multi_output_regression:1.0.0
```

## 6. 注销模型

```bash
curl -X DELETE http://localhost:8080/models/multi_output_regression:1.0.0
```

## 优势总结

1. **标准化协议**: 统一的配置文件格式，团队成员遵循相同规范
2. **自动API生成**: 根据配置自动生成RESTful API接口
3. **输入验证**: 自动验证输入数据格式和范围
4. **输出格式化**: 根据输出类型自动格式化结果
5. **批处理支持**: 可选的批量预测功能
6. **版本管理**: 支持模型版本控制
7. **动态注册**: 运行时动态注册和注销模型
8. **Swagger文档**: 自动生成API文档

## 团队协作流程

1. **模型开发者**: 训练模型并创建配置文件
2. **配置验证**: 系统自动验证配置文件格式
3. **模型注册**: 通过API注册模型和配置
4. **API生成**: 系统自动生成标准化API
5. **文档更新**: Swagger文档自动更新
6. **客户端调用**: 其他服务使用标准化API调用
