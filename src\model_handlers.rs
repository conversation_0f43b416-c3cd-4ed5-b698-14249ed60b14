use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info};
use utoipa::ToSchema;

use crate::api_generator::{ApiGenerator, StandardPredictRequest, StandardPredictResponse};
use crate::model_config::{ModelConfig, ModelRegistry};
use crate::models::ErrorResponse;
use crate::onnx_engine::SharedOnnxEngine;

/// 扩展的应用状态，包含模型注册表
#[derive(Clone)]
pub struct ExtendedAppState {
    pub engine: SharedOnnxEngine,
    pub registry: Arc<RwLock<ModelRegistry>>,
}

/// 模型注册请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct RegisterModelRequest {
    /// 模型文件路径
    pub model_path: String,
    /// 配置文件路径
    pub config_path: String,
}

/// 模型注册响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct RegisterModelResponse {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 模型ID
    pub model_id: Option<String>,
    /// 生成的API端点
    pub endpoints: Option<Vec<String>>,
}

/// 模型列表响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelListResponse {
    /// 已注册的模型列表
    pub models: Vec<ModelSummary>,
    /// 总数
    pub total: usize,
}

/// 模型摘要信息
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelSummary {
    /// 模型ID
    pub model_id: String,
    /// 模型名称
    pub name: String,
    /// 版本
    pub version: String,
    /// 描述
    pub description: String,
    /// 状态
    pub status: String,
    /// API端点
    pub endpoints: Vec<String>,
}

/// 注册新模型
#[utoipa::path(
    post,
    path = "/models/register",
    request_body = RegisterModelRequest,
    responses(
        (status = 200, description = "Model registered successfully", body = RegisterModelResponse),
        (status = 400, description = "Invalid request", body = ErrorResponse),
        (status = 500, description = "Internal server error", body = ErrorResponse)
    ),
    tag = "Model Management"
)]
pub async fn register_model(
    State(state): State<ExtendedAppState>,
    Json(request): Json<RegisterModelRequest>,
) -> Result<Json<RegisterModelResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!(
        "Registering model: {} with config: {}",
        request.model_path, request.config_path
    );

    // 加载模型配置
    let config = match ModelConfig::from_file(&request.config_path) {
        Ok(config) => config,
        Err(e) => {
            error!("Failed to load model config: {}", e);
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error_code: "CONFIG_LOAD_ERROR".to_string(),
                    message: format!("Failed to load config file: {}", e),
                    request_id: Some(uuid::Uuid::new_v4().to_string()),
                }),
            ));
        }
    };

    // 加载ONNX模型到引擎
    let _model_info = {
        let mut engine = state.engine.write().await;
        match engine
            .load_model(&request.model_path, Some(config.model_info.name.clone()))
            .await
        {
            Ok(info) => info,
            Err(e) => {
                error!("Failed to load ONNX model: {}", e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error_code: "MODEL_LOAD_ERROR".to_string(),
                        message: format!("Failed to load ONNX model: {}", e),
                        request_id: Some(uuid::Uuid::new_v4().to_string()),
                    }),
                ));
            }
        }
    };

    // 注册模型配置
    let model_id = config.get_model_id();
    {
        let mut registry = state.registry.write().await;
        if let Err(e) = registry.register(config.clone()) {
            error!("Failed to register model config: {}", e);
            return Err((
                StatusCode::CONFLICT,
                Json(ErrorResponse {
                    error_code: "MODEL_ALREADY_EXISTS".to_string(),
                    message: format!("Model already registered: {}", e),
                    request_id: Some(uuid::Uuid::new_v4().to_string()),
                }),
            ));
        }
    }

    // 生成API端点列表
    let endpoints = vec![config.get_api_path("info"), config.get_api_path("predict")];

    let mut all_endpoints = endpoints.clone();
    if config.is_batch_enabled() {
        all_endpoints.push(config.get_api_path("batch_predict"));
    }

    info!(
        "Model {} registered successfully with {} endpoints",
        model_id,
        all_endpoints.len()
    );

    Ok(Json(RegisterModelResponse {
        success: true,
        message: format!("Model {} registered successfully", model_id),
        model_id: Some(model_id),
        endpoints: Some(all_endpoints),
    }))
}

/// 获取已注册的模型列表
#[utoipa::path(
    get,
    path = "/models",
    responses(
        (status = 200, description = "List of registered models", body = ModelListResponse)
    ),
    tag = "Model Management"
)]
pub async fn list_registered_models(
    State(state): State<ExtendedAppState>,
) -> Json<ModelListResponse> {
    let registry = state.registry.read().await;
    let models = registry.list_models();

    let model_summaries: Vec<ModelSummary> = models
        .iter()
        .map(|config| {
            let mut endpoints = vec![config.get_api_path("info"), config.get_api_path("predict")];

            if config.is_batch_enabled() {
                endpoints.push(config.get_api_path("batch_predict"));
            }

            ModelSummary {
                model_id: config.get_model_id(),
                name: config.model_info.name.clone(),
                version: config.model_info.version.clone(),
                description: config.model_info.description.clone(),
                status: "active".to_string(), // 简化状态管理
                endpoints,
            }
        })
        .collect();

    Json(ModelListResponse {
        total: model_summaries.len(),
        models: model_summaries,
    })
}

/// 获取特定模型的详细信息
#[utoipa::path(
    get,
    path = "/models/{model_id}",
    params(
        ("model_id" = String, Path, description = "Model ID")
    ),
    responses(
        (status = 200, description = "Model details", body = ModelConfig),
        (status = 404, description = "Model not found", body = ErrorResponse)
    ),
    tag = "Model Management"
)]
pub async fn get_model_details(
    Path(model_id): Path<String>,
    State(state): State<ExtendedAppState>,
) -> Result<Json<ModelConfig>, (StatusCode, Json<ErrorResponse>)> {
    let registry = state.registry.read().await;

    match registry.get(&model_id) {
        Some(config) => Ok(Json(config.clone())),
        None => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error_code: "MODEL_NOT_FOUND".to_string(),
                message: format!("Model {} not found", model_id),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            }),
        )),
    }
}

/// 注销模型
#[utoipa::path(
    delete,
    path = "/models/{model_id}",
    params(
        ("model_id" = String, Path, description = "Model ID")
    ),
    responses(
        (status = 200, description = "Model unregistered successfully"),
        (status = 404, description = "Model not found", body = ErrorResponse)
    ),
    tag = "Model Management"
)]
pub async fn unregister_model(
    Path(model_id): Path<String>,
    State(state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    let mut registry = state.registry.write().await;

    match registry.unregister(&model_id) {
        Some(_) => {
            info!("Model {} unregistered successfully", model_id);
            Ok(Json(serde_json::json!({
                "success": true,
                "message": format!("Model {} unregistered successfully", model_id)
            })))
        }
        None => Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error_code: "MODEL_NOT_FOUND".to_string(),
                message: format!("Model {} not found", model_id),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            }),
        )),
    }
}

/// 标准化预测接口
#[utoipa::path(
    post,
    path = "/models/{model_id}/predict",
    params(
        ("model_id" = String, Path, description = "Model ID")
    ),
    request_body = StandardPredictRequest,
    responses(
        (status = 200, description = "Prediction result", body = StandardPredictResponse),
        (status = 404, description = "Model not found", body = ErrorResponse),
        (status = 400, description = "Invalid input", body = ErrorResponse)
    ),
    tag = "Prediction"
)]
pub async fn standard_predict(
    Path(model_id): Path<String>,
    State(state): State<ExtendedAppState>,
    Json(request): Json<StandardPredictRequest>,
) -> Result<Json<StandardPredictResponse>, (StatusCode, Json<ErrorResponse>)> {
    // 获取模型配置
    let config = {
        let registry = state.registry.read().await;
        match registry.get(&model_id) {
            Some(config) => config.clone(),
            None => {
                return Err((
                    StatusCode::NOT_FOUND,
                    Json(ErrorResponse {
                        error_code: "MODEL_NOT_FOUND".to_string(),
                        message: format!("Model {} not found", model_id),
                        request_id: Some(uuid::Uuid::new_v4().to_string()),
                    }),
                ));
            }
        }
    };

    // 创建API生成器并验证输入
    let api_generator = ApiGenerator::new(ModelRegistry::new()); // 临时创建
    let validated_inputs = match api_generator.validate_inputs(&request.inputs, &config.inputs) {
        Ok(inputs) => inputs,
        Err(e) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error_code: "INPUT_VALIDATION_ERROR".to_string(),
                    message: format!("Input validation failed: {}", e),
                    request_id: Some(uuid::Uuid::new_v4().to_string()),
                }),
            ));
        }
    };

    // 执行推理
    let inference_request = crate::models::InferenceRequest {
        inputs: validated_inputs,
        output_names: config.outputs.iter().map(|o| o.name.clone()).collect(),
    };

    let inference_response = {
        let mut engine = state.engine.write().await;
        match engine.inference(inference_request).await {
            Ok(response) => response,
            Err(e) => {
                error!("Inference failed: {}", e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error_code: "INFERENCE_ERROR".to_string(),
                        message: format!("Inference failed: {}", e),
                        request_id: Some(uuid::Uuid::new_v4().to_string()),
                    }),
                ));
            }
        }
    };

    // 格式化输出
    let formatted_outputs =
        api_generator.format_outputs(&inference_response.outputs, &config.outputs);

    Ok(Json(StandardPredictResponse {
        predictions: formatted_outputs,
        model_info: crate::api_generator::ModelMetadata {
            name: config.model_info.name,
            version: config.model_info.version,
            description: config.model_info.description,
        },
        inference_time_ms: inference_response.inference_time_ms,
        request_id: inference_response.request_id,
    }))
}
