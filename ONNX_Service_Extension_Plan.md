# ONNX Service 标准化协议扩展方案

## 方案概述

基于当前ONNX Service实现，设计一套标准化协议，通过ONNX模型文件 + 配置文件的方式，自动生成标准化API接口，提高团队协作效率。

## 核心设计理念

**配置驱动 + 自动生成**：团队成员只需提供ONNX模型文件和简单的配置文件，系统自动解析并生成标准化的REST API接口。

## 协议定义

### 配置文件格式 (model_config.json)

```json
{
  "model": {
    "name": "模型名称",
    "version": "1.0.0",
    "description": "模型描述"
  },
  "inputs": [
    {
      "name": "输入张量名称",
      "dtype": "数据类型",
      "shape": "张量形状"
    }
  ],
  "outputs": [
    {
      "name": "输出张量名称", 
      "dtype": "数据类型",
      "shape": "张量形状"
    }
  ]
}
```

### 您的train.py模型配置示例

```json
{
  "model": {
    "name": "multi_output_regression",
    "version": "1.0.0", 
    "description": "多输出回归模型，2个特征输入，3个目标输出"
  },
  "inputs": [
    {
      "name": "float_input",
      "dtype": "float32",
      "shape": [-1, 2]
    }
  ],
  "outputs": [
    {
      "name": "variable_0",
      "dtype": "float32",
      "shape": [-1, 1]
    },
    {
      "name": "variable_1", 
      "dtype": "float32",
      "shape": [-1, 1]
    },
    {
      "name": "variable_2",
      "dtype": "float32", 
      "shape": [-1, 1]
    }
  ]
}
```

## 系统架构

```
模型开发者
    ↓
┌─────────────────┐    ┌─────────────────┐
│   ONNX模型文件   │    │  配置文件        │
│   model.onnx    │    │ model_config.json│
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     ↓
         ┌─────────────────────────┐
         │   ONNX Service Core     │
         │   + 协议扩展模块         │
         └─────────────────────────┘
                     ↓
         ┌─────────────────────────┐
         │   标准化REST API         │
         │   + Swagger文档          │
         └─────────────────────────┘
                     ↓
              API客户端调用
```

## 核心功能模块

### 1. 配置解析器 (ConfigParser)
- 解析model_config.json文件
- 验证配置格式和字段完整性
- 与ONNX模型元数据进行匹配验证

### 2. 模型注册器 (ModelRegistry) 
- 管理已注册的模型配置
- 提供模型查询和管理接口
- 支持模型版本控制

### 3. API生成器 (APIGenerator)
- 根据配置自动生成REST API路由
- 自动生成输入输出数据结构
- 自动更新Swagger API文档

### 4. 标准化处理器 (StandardHandler)
- 统一的请求响应格式
- 自动输入数据验证和类型转换
- 统一的错误处理机制

## 生成的API接口

基于配置文件，系统将自动生成以下标准化接口：

### 模型管理接口
- `POST /models/register` - 注册新模型
- `GET /models` - 获取模型列表  
- `GET /models/{model_name}` - 获取模型信息
- `DELETE /models/{model_name}` - 注销模型

### 预测接口
- `POST /models/{model_name}/predict` - 单次预测
- `POST /models/{model_name}/batch` - 批量预测

### 标准化请求格式
```json
{
  "inputs": {
    "float_input": [[1.5, 2.3]]
  }
}
```

### 标准化响应格式
```json
{
  "outputs": {
    "variable_0": [1.234],
    "variable_1": [2.567], 
    "variable_2": [0.891]
  },
  "model_info": {
    "name": "multi_output_regression",
    "version": "1.0.0"
  },
  "inference_time_ms": 15.6
}
```

## 实现步骤

### 阶段1：核心协议实现
1. 定义配置文件数据结构
2. 实现配置文件解析和验证
3. 实现模型注册管理功能

### 阶段2：API自动生成
1. 实现基于配置的路由生成
2. 实现标准化请求响应处理
3. 集成到现有ONNX Service

### 阶段3：完善和优化
1. 添加错误处理和日志
2. 更新Swagger文档生成
3. 性能优化和测试

## 团队协作流程

### 模型开发者职责
1. 训练并导出ONNX模型文件
2. 编写对应的model_config.json配置文件
3. 通过API注册模型到服务

### 服务维护者职责  
1. 维护ONNX Service核心功能
2. 管理已注册的模型
3. 监控服务性能和稳定性

### API使用者职责
1. 查看自动生成的Swagger文档
2. 使用标准化API接口调用模型
3. 处理标准化的响应数据

## 方案优势

### 1. 简化协作
- 统一的配置文件格式
- 标准化的API接口
- 自动生成的文档

### 2. 降低成本
- 减少手动配置工作
- 避免重复开发
- 统一的错误处理

### 3. 提高质量
- 配置文件格式验证
- 自动输入数据验证
- 一致的接口规范

### 4. 易于维护
- 配置与代码分离
- 版本控制支持
- 模块化设计

## 技术可行性

### 基于现有架构
- 复用现有OnnxEngine核心功能
- 扩展现有的handlers模块
- 保持现有API向后兼容

### 技术栈兼容
- 利用Rust强类型系统
- 集成现有的Axum Web框架
- 复用现有的Swagger文档系统

### 性能影响最小
- 配置解析仅在注册时执行
- 运行时性能与现有系统相同
- 内存开销可控

## 总结

该方案通过引入简单的配置文件协议，在不影响现有系统的基础上，提供了标准化的模型服务化能力。核心思想是"配置驱动，自动生成"，能够显著提高团队协作效率，降低模型部署成本，同时保持系统的简洁性和可维护性。

配置文件只包含最核心的字段（模型信息、输入输出规格），避免了过度设计，确保方案的实用性和可操作性。
