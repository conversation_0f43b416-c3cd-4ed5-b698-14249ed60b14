use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use utoipa::ToSchema;

/// 模型配置文件结构
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelConfig {
    pub model_info: ModelInfo,
    pub runtime: Option<RuntimeConfig>,
    pub inputs: Vec<TensorSpec>,
    pub outputs: Vec<TensorSpec>,
    pub preprocessing: Option<PreprocessingConfig>,
    pub postprocessing: Option<PostprocessingConfig>,
    pub api: Option<ApiConfig>,
}

/// 模型基本信息
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: Option<String>,
    pub created_at: Option<String>,
    pub tags: Option<Vec<String>>,
}

/// 运行时配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct RuntimeConfig {
    #[serde(default = "default_batch_size")]
    pub max_batch_size: i32,
    #[serde(default = "default_timeout")]
    pub timeout_ms: i32,
    #[serde(default = "default_optimization_level")]
    pub optimization_level: u8,
}

/// 张量规格定义
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TensorSpec {
    pub name: String,
    pub dtype: String,
    pub shape: Vec<i64>,
    pub description: Option<String>,
    pub example: Option<serde_json::Value>,
    pub validation: Option<ValidationConfig>,
    pub interpretation: Option<InterpretationConfig>,
}

/// 输入验证配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ValidationConfig {
    pub min_value: Option<f64>,
    pub max_value: Option<f64>,
    pub required: Option<bool>,
}

/// 输出解释配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct InterpretationConfig {
    pub r#type: String, // classification, regression, probability, embedding
    pub labels: Option<Vec<String>>,
    pub unit: Option<String>,
}

/// 预处理配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct PreprocessingConfig {
    pub normalization: Option<NormalizationConfig>,
    pub resize: Option<ResizeConfig>,
}

/// 归一化配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct NormalizationConfig {
    pub mean: Vec<f64>,
    pub std: Vec<f64>,
}

/// 尺寸调整配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ResizeConfig {
    pub width: i32,
    pub height: i32,
}

/// 后处理配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct PostprocessingConfig {
    pub threshold: Option<f64>,
    pub top_k: Option<i32>,
}

/// API生成配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ApiConfig {
    #[serde(default = "default_endpoint_prefix")]
    pub endpoint_prefix: String,
    #[serde(default = "default_enable_batch")]
    pub enable_batch: bool,
    pub custom_routes: Option<Vec<CustomRoute>>,
}

/// 自定义路由配置
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct CustomRoute {
    pub path: String,
    pub method: String,
    pub description: String,
}

// 默认值函数
fn default_batch_size() -> i32 { 1 }
fn default_timeout() -> i32 { 30000 }
fn default_optimization_level() -> u8 { 3 }
fn default_endpoint_prefix() -> String { "/models".to_string() }
fn default_enable_batch() -> bool { true }

impl ModelConfig {
    /// 从JSON文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = fs::read_to_string(path.as_ref())
            .map_err(|e| anyhow!("Failed to read config file: {}", e))?;
        
        let config: ModelConfig = serde_json::from_str(&content)
            .map_err(|e| anyhow!("Failed to parse config JSON: {}", e))?;
        
        config.validate()?;
        Ok(config)
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        // 验证模型名称
        if self.model_info.name.is_empty() {
            return Err(anyhow!("Model name cannot be empty"));
        }

        // 验证版本格式
        if !self.is_valid_version(&self.model_info.version) {
            return Err(anyhow!("Invalid version format: {}", self.model_info.version));
        }

        // 验证输入输出不为空
        if self.inputs.is_empty() {
            return Err(anyhow!("Model must have at least one input"));
        }
        if self.outputs.is_empty() {
            return Err(anyhow!("Model must have at least one output"));
        }

        // 验证张量规格
        for input in &self.inputs {
            self.validate_tensor_spec(input, "input")?;
        }
        for output in &self.outputs {
            self.validate_tensor_spec(output, "output")?;
        }

        Ok(())
    }

    /// 验证版本号格式 (x.y.z)
    fn is_valid_version(&self, version: &str) -> bool {
        let parts: Vec<&str> = version.split('.').collect();
        if parts.len() != 3 {
            return false;
        }
        parts.iter().all(|part| part.parse::<u32>().is_ok())
    }

    /// 验证张量规格
    fn validate_tensor_spec(&self, spec: &TensorSpec, spec_type: &str) -> Result<()> {
        if spec.name.is_empty() {
            return Err(anyhow!("{} name cannot be empty", spec_type));
        }

        let valid_dtypes = ["float32", "float64", "int32", "int64", "bool"];
        if !valid_dtypes.contains(&spec.dtype.as_str()) {
            return Err(anyhow!("Invalid dtype '{}' for {}: {}", spec.dtype, spec_type, spec.name));
        }

        if spec.shape.is_empty() {
            return Err(anyhow!("Shape cannot be empty for {}: {}", spec_type, spec.name));
        }

        Ok(())
    }

    /// 获取模型的唯一标识符
    pub fn get_model_id(&self) -> String {
        format!("{}:{}", self.model_info.name, self.model_info.version)
    }

    /// 生成API端点路径
    pub fn get_api_path(&self, endpoint: &str) -> String {
        let prefix = self.api.as_ref()
            .map(|api| api.endpoint_prefix.clone())
            .unwrap_or_else(|| "/models".to_string());
        
        format!("{}/{}/{}", prefix, self.model_info.name, endpoint)
    }

    /// 检查是否启用批处理
    pub fn is_batch_enabled(&self) -> bool {
        self.api.as_ref()
            .map(|api| api.enable_batch)
            .unwrap_or(true)
    }
}

/// 模型注册表，管理多个模型配置
#[derive(Debug, Clone)]
pub struct ModelRegistry {
    models: HashMap<String, ModelConfig>,
}

impl ModelRegistry {
    pub fn new() -> Self {
        Self {
            models: HashMap::new(),
        }
    }

    /// 注册模型配置
    pub fn register(&mut self, config: ModelConfig) -> Result<()> {
        let model_id = config.get_model_id();
        
        if self.models.contains_key(&model_id) {
            return Err(anyhow!("Model {} already registered", model_id));
        }

        self.models.insert(model_id, config);
        Ok(())
    }

    /// 获取模型配置
    pub fn get(&self, model_id: &str) -> Option<&ModelConfig> {
        self.models.get(model_id)
    }

    /// 获取所有已注册的模型
    pub fn list_models(&self) -> Vec<&ModelConfig> {
        self.models.values().collect()
    }

    /// 移除模型
    pub fn unregister(&mut self, model_id: &str) -> Option<ModelConfig> {
        self.models.remove(model_id)
    }
}

impl Default for ModelRegistry {
    fn default() -> Self {
        Self::new()
    }
}
