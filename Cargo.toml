[package]
name = "onnx_service"
version = "0.1.0"
edition = "2021"

[dependencies]
# ONNX Runtime - 使用本地库而不是下载
ort = { version = "=2.0.0-rc.10", default-features = false, features = ["std"] }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }

# Async runtime
tokio = { version = "1.40", features = ["macros", "rt-multi-thread", "net", "fs", "io-util", "time"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Environment variables
dotenv = "0.15"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# OpenAPI/Swagger
utoipa = { version = "4.0", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "6.0", features = ["axum"] }

# UUID generation
uuid = { version = "1.0", features = ["v4"] }
