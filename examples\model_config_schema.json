{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ONNX Model Configuration Schema", "description": "标准化的ONNX模型配置文件格式", "type": "object", "required": ["model_info", "inputs", "outputs"], "properties": {"model_info": {"type": "object", "description": "模型基本信息", "required": ["name", "version", "description"], "properties": {"name": {"type": "string", "description": "模型名称，用于API路径生成"}, "version": {"type": "string", "description": "模型版本号", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "description": {"type": "string", "description": "模型功能描述"}, "author": {"type": "string", "description": "模型作者"}, "created_at": {"type": "string", "format": "date-time", "description": "模型创建时间"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "模型标签"}}}, "runtime": {"type": "object", "description": "运行时配置", "properties": {"max_batch_size": {"type": "integer", "minimum": 1, "default": 1, "description": "最大批处理大小"}, "timeout_ms": {"type": "integer", "minimum": 100, "default": 30000, "description": "推理超时时间(毫秒)"}, "optimization_level": {"type": "integer", "minimum": 0, "maximum": 3, "default": 3, "description": "ONNX Runtime优化级别"}}}, "inputs": {"type": "array", "description": "输入张量定义", "minItems": 1, "items": {"type": "object", "required": ["name", "dtype", "shape"], "properties": {"name": {"type": "string", "description": "输入张量名称"}, "dtype": {"type": "string", "enum": ["float32", "float64", "int32", "int64", "bool"], "description": "数据类型"}, "shape": {"type": "array", "items": {"type": "integer"}, "description": "张量形状，-1表示动态维度"}, "description": {"type": "string", "description": "输入参数描述"}, "example": {"description": "示例数据"}, "validation": {"type": "object", "description": "输入验证规则", "properties": {"min_value": {"type": "number"}, "max_value": {"type": "number"}, "required": {"type": "boolean", "default": true}}}}}}, "outputs": {"type": "array", "description": "输出张量定义", "minItems": 1, "items": {"type": "object", "required": ["name", "dtype", "shape"], "properties": {"name": {"type": "string", "description": "输出张量名称"}, "dtype": {"type": "string", "enum": ["float32", "float64", "int32", "int64", "bool"], "description": "数据类型"}, "shape": {"type": "array", "items": {"type": "integer"}, "description": "张量形状，-1表示动态维度"}, "description": {"type": "string", "description": "输出结果描述"}, "interpretation": {"type": "object", "description": "输出结果解释", "properties": {"type": {"type": "string", "enum": ["classification", "regression", "probability", "embedding"], "description": "输出类型"}, "labels": {"type": "array", "items": {"type": "string"}, "description": "分类标签(仅classification类型)"}, "unit": {"type": "string", "description": "数值单位(仅regression类型)"}}}}}}, "preprocessing": {"type": "object", "description": "预处理配置", "properties": {"normalization": {"type": "object", "properties": {"mean": {"type": "array", "items": {"type": "number"}}, "std": {"type": "array", "items": {"type": "number"}}}}, "resize": {"type": "object", "properties": {"width": {"type": "integer"}, "height": {"type": "integer"}}}}}, "postprocessing": {"type": "object", "description": "后处理配置", "properties": {"threshold": {"type": "number", "description": "置信度阈值"}, "top_k": {"type": "integer", "description": "返回前K个结果"}}}, "api": {"type": "object", "description": "API生成配置", "properties": {"endpoint_prefix": {"type": "string", "description": "API端点前缀", "default": "/models"}, "enable_batch": {"type": "boolean", "default": true, "description": "是否启用批处理接口"}, "custom_routes": {"type": "array", "items": {"type": "object", "properties": {"path": {"type": "string"}, "method": {"type": "string", "enum": ["GET", "POST"]}, "description": {"type": "string"}}}}}}}}