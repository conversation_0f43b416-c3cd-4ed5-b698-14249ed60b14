use anyhow::{anyhow, Result};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use utoipa::ToSchema;

use crate::model_config::{ModelConfig, ModelRegistry, TensorSpec};
use crate::models::{ErrorResponse, TensorData};
use crate::onnx_engine::SharedOnnxEngine;

/// 标准化的预测请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct StandardPredictRequest {
    /// 输入数据，根据模型配置自动验证
    pub inputs: HashMap<String, serde_json::Value>,
    /// 可选的配置参数
    pub options: Option<PredictOptions>,
}

/// 预测选项
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct PredictOptions {
    /// 是否返回详细信息
    pub verbose: Option<bool>,
    /// 自定义阈值
    pub threshold: Option<f64>,
    /// 返回前K个结果
    pub top_k: Option<i32>,
}

/// 标准化的预测响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct StandardPredictResponse {
    /// 预测结果
    pub predictions: HashMap<String, serde_json::Value>,
    /// 模型信息
    pub model_info: ModelMetadata,
    /// 推理时间
    pub inference_time_ms: f64,
    /// 请求ID
    pub request_id: String,
}

/// 模型元数据
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ModelMetadata {
    pub name: String,
    pub version: String,
    pub description: String,
}

/// 批量预测请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct BatchPredictRequest {
    /// 批量输入数据
    pub batch_inputs: Vec<HashMap<String, serde_json::Value>>,
    /// 预测选项
    pub options: Option<PredictOptions>,
}

/// 批量预测响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct BatchPredictResponse {
    /// 批量预测结果
    pub batch_predictions: Vec<HashMap<String, serde_json::Value>>,
    /// 模型信息
    pub model_info: ModelMetadata,
    /// 总推理时间
    pub total_inference_time_ms: f64,
    /// 请求ID
    pub request_id: String,
}

/// API生成器
pub struct ApiGenerator {
    registry: ModelRegistry,
}

impl ApiGenerator {
    pub fn new(registry: ModelRegistry) -> Self {
        Self { registry }
    }

    /// 为所有注册的模型生成API路由
    pub fn generate_routes(&self, engine: SharedOnnxEngine) -> Router {
        let mut router = Router::new();

        // 添加模型列表接口
        router = router.route("/models", get(list_models));

        // 为每个模型生成专用路由
        for config in self.registry.list_models() {
            let model_routes = self.generate_model_routes(config, engine.clone());
            router = router.nest(&format!("/models/{}", config.model_info.name), model_routes);
        }

        router
    }

    /// 为单个模型生成路由
    fn generate_model_routes(&self, config: &ModelConfig, engine: SharedOnnxEngine) -> Router {
        let mut router = Router::new();

        // 基础路由
        router = router
            .route("/info", get(get_model_info))
            .route("/predict", post(predict))
            .with_state((engine.clone(), config.clone()));

        // 如果启用批处理，添加批处理路由
        if config.is_batch_enabled() {
            router = router
                .route("/batch_predict", post(batch_predict))
                .with_state((engine.clone(), config.clone()));
        }

        // 添加自定义路由
        if let Some(api_config) = &config.api {
            if let Some(custom_routes) = &api_config.custom_routes {
                for route in custom_routes {
                    match route.method.as_str() {
                        "GET" => {
                            router = router.route(&route.path, get(custom_handler));
                        }
                        "POST" => {
                            router = router.route(&route.path, post(custom_handler));
                        }
                        _ => {
                            // 忽略不支持的方法
                        }
                    }
                }
            }
        }

        router
    }

    /// 验证输入数据
    pub fn validate_inputs(
        &self,
        inputs: &HashMap<String, serde_json::Value>,
        input_specs: &[TensorSpec],
    ) -> Result<HashMap<String, TensorData>> {
        let mut validated_inputs = HashMap::new();

        for spec in input_specs {
            let input_value = inputs.get(&spec.name)
                .ok_or_else(|| anyhow!("Missing required input: {}", spec.name))?;

            let tensor_data = self.convert_to_tensor_data(input_value, spec)?;
            validated_inputs.insert(spec.name.clone(), tensor_data);
        }

        Ok(validated_inputs)
    }

    /// 将JSON值转换为TensorData
    fn convert_to_tensor_data(
        &self,
        value: &serde_json::Value,
        spec: &TensorSpec,
    ) -> Result<TensorData> {
        match value {
            serde_json::Value::Array(arr) => {
                let data = self.flatten_array(arr)?;
                let shape = self.infer_shape(arr, &spec.shape)?;
                
                Ok(TensorData::new(
                    spec.dtype.clone(),
                    shape,
                    data,
                ))
            }
            serde_json::Value::Number(num) => {
                let data = vec![num.as_f64().unwrap_or(0.0) as f32];
                Ok(TensorData::new(
                    spec.dtype.clone(),
                    vec![1],
                    data,
                ))
            }
            _ => Err(anyhow!("Unsupported input format for {}", spec.name)),
        }
    }

    /// 递归展平多维数组
    fn flatten_array(&self, arr: &[serde_json::Value]) -> Result<Vec<f32>> {
        let mut result = Vec::new();
        
        for item in arr {
            match item {
                serde_json::Value::Number(num) => {
                    result.push(num.as_f64().unwrap_or(0.0) as f32);
                }
                serde_json::Value::Array(sub_arr) => {
                    result.extend(self.flatten_array(sub_arr)?);
                }
                _ => return Err(anyhow!("Invalid array element type")),
            }
        }
        
        Ok(result)
    }

    /// 推断实际形状
    fn infer_shape(
        &self,
        arr: &[serde_json::Value],
        expected_shape: &[i64],
    ) -> Result<Vec<i64>> {
        let mut shape = Vec::new();
        let mut current = arr;
        
        loop {
            shape.push(current.len() as i64);
            
            if let Some(serde_json::Value::Array(sub_arr)) = current.first() {
                current = sub_arr;
            } else {
                break;
            }
        }

        // 验证形状兼容性
        if expected_shape.len() != shape.len() {
            return Err(anyhow!("Shape dimension mismatch"));
        }

        for (i, (&expected, &actual)) in expected_shape.iter().zip(shape.iter()).enumerate() {
            if expected != -1 && expected != actual {
                return Err(anyhow!(
                    "Shape mismatch at dimension {}: expected {}, got {}",
                    i, expected, actual
                ));
            }
        }

        Ok(shape)
    }

    /// 格式化输出结果
    pub fn format_outputs(
        &self,
        outputs: &HashMap<String, TensorData>,
        output_specs: &[TensorSpec],
    ) -> HashMap<String, serde_json::Value> {
        let mut formatted = HashMap::new();

        for (name, tensor_data) in outputs {
            if let Some(spec) = output_specs.iter().find(|s| s.name == *name) {
                let formatted_value = self.format_tensor_output(tensor_data, spec);
                formatted.insert(name.clone(), formatted_value);
            }
        }

        formatted
    }

    /// 格式化单个张量输出
    fn format_tensor_output(&self, tensor_data: &TensorData, spec: &TensorSpec) -> serde_json::Value {
        // 根据输出类型进行特殊格式化
        if let Some(interpretation) = &spec.interpretation {
            match interpretation.r#type.as_str() {
                "classification" => {
                    if let Some(labels) = &interpretation.labels {
                        return self.format_classification_output(tensor_data, labels);
                    }
                }
                "probability" => {
                    return self.format_probability_output(tensor_data);
                }
                _ => {}
            }
        }

        // 默认格式化
        serde_json::Value::Array(
            tensor_data.data.iter()
                .map(|&x| serde_json::Value::Number(serde_json::Number::from_f64(x as f64).unwrap()))
                .collect()
        )
    }

    /// 格式化分类输出
    fn format_classification_output(&self, tensor_data: &TensorData, labels: &[String]) -> serde_json::Value {
        let mut result = Vec::new();
        
        for (i, &score) in tensor_data.data.iter().enumerate() {
            if i < labels.len() {
                result.push(serde_json::json!({
                    "label": labels[i],
                    "score": score
                }));
            }
        }
        
        serde_json::Value::Array(result)
    }

    /// 格式化概率输出
    fn format_probability_output(&self, tensor_data: &TensorData) -> serde_json::Value {
        serde_json::json!({
            "probabilities": tensor_data.data,
            "max_probability": tensor_data.data.iter().fold(0.0f32, |a, &b| a.max(b)),
            "predicted_class": tensor_data.data.iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i)
                .unwrap_or(0)
        })
    }
}

// 路由处理函数
async fn list_models() -> Json<Vec<String>> {
    // 实现获取所有模型列表的逻辑
    Json(vec!["model1".to_string(), "model2".to_string()])
}

async fn get_model_info(
    Path(model_name): Path<String>,
) -> Result<Json<ModelMetadata>, (StatusCode, Json<ErrorResponse>)> {
    // 实现获取模型信息的逻辑
    Ok(Json(ModelMetadata {
        name: model_name,
        version: "1.0.0".to_string(),
        description: "Model description".to_string(),
    }))
}

async fn predict(
    State((engine, config)): State<(SharedOnnxEngine, ModelConfig)>,
    Json(request): Json<StandardPredictRequest>,
) -> Result<Json<StandardPredictResponse>, (StatusCode, Json<ErrorResponse>)> {
    // 实现预测逻辑
    let response = StandardPredictResponse {
        predictions: HashMap::new(),
        model_info: ModelMetadata {
            name: config.model_info.name,
            version: config.model_info.version,
            description: config.model_info.description,
        },
        inference_time_ms: 0.0,
        request_id: uuid::Uuid::new_v4().to_string(),
    };
    
    Ok(Json(response))
}

async fn batch_predict(
    State((engine, config)): State<(SharedOnnxEngine, ModelConfig)>,
    Json(request): Json<BatchPredictRequest>,
) -> Result<Json<BatchPredictResponse>, (StatusCode, Json<ErrorResponse>)> {
    // 实现批量预测逻辑
    let response = BatchPredictResponse {
        batch_predictions: vec![],
        model_info: ModelMetadata {
            name: config.model_info.name,
            version: config.model_info.version,
            description: config.model_info.description,
        },
        total_inference_time_ms: 0.0,
        request_id: uuid::Uuid::new_v4().to_string(),
    };
    
    Ok(Json(response))
}

async fn custom_handler() -> &'static str {
    "Custom endpoint"
}
