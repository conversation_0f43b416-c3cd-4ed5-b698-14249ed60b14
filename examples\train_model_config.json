{"model_info": {"name": "multi_output_regression", "version": "1.0.0", "description": "多输出回归模型，基于RandomForest实现，2个特征输入，3个目标输出", "author": "ML Team", "created_at": "2024-01-15T10:30:00Z", "tags": ["regression", "multi-output", "random-forest"]}, "runtime": {"max_batch_size": 32, "timeout_ms": 5000, "optimization_level": 3}, "inputs": [{"name": "float_input", "dtype": "float32", "shape": [-1, 2], "description": "两个特征的输入数据，支持批处理", "example": [[1.5, 2.3], [0.8, 1.9]], "validation": {"min_value": -10.0, "max_value": 10.0, "required": true}}], "outputs": [{"name": "variable_0", "dtype": "float32", "shape": [-1, 1], "description": "第一个回归目标的预测值", "interpretation": {"type": "regression", "unit": "numeric"}}, {"name": "variable_1", "dtype": "float32", "shape": [-1, 1], "description": "第二个回归目标的预测值", "interpretation": {"type": "regression", "unit": "numeric"}}, {"name": "variable_2", "dtype": "float32", "shape": [-1, 1], "description": "第三个回归目标的预测值", "interpretation": {"type": "regression", "unit": "numeric"}}], "preprocessing": {"normalization": {"mean": [0.0, 0.0], "std": [1.0, 1.0]}}, "postprocessing": {"threshold": 0.0}, "api": {"endpoint_prefix": "/models", "enable_batch": true, "custom_routes": [{"path": "/predict", "method": "POST", "description": "单次预测接口"}, {"path": "/batch_predict", "method": "POST", "description": "批量预测接口"}]}}