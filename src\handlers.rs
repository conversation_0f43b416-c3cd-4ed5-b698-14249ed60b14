use axum::{extract::State, http::StatusCode, response::J<PERSON>};
use tracing::{error, info};

use crate::model_config::ModelConfig;
use crate::models::{
    ErrorResponse, HealthResponse, InferenceRequest, InferenceResponse, LoadModelRequest,
    LoadModelResponse, ModelInfo,
};
use crate::onnx_engine::SharedOnnxEngine;

/// 应用状态
#[derive(Clone)]
pub struct AppState {
    pub engine: SharedOnnxEngine,
}

/// 健康检查端点
#[utoipa::path(
    get,
    path = "/health",
    responses(
        (status = 200, description = "Service is healthy", body = HealthResponse)
    ),
    tag = "Health"
)]
pub async fn health_check(
    State(state): State<AppState>,
) -> Result<Json<HealthResponse>, (StatusCode, Json<ErrorResponse>)> {
    let engine = state.engine.read().await;

    let model_status = if engine.is_loaded() {
        "loaded"
    } else {
        "not_loaded"
    };

    let response = HealthResponse {
        model_status: model_status.to_string(),
        ..Default::default()
    };

    Ok(Json(response))
}

/// 获取模型信息端点
#[utoipa::path(
    get,
    path = "/model/info",
    responses(
        (status = 200, description = "Model information", body = ModelInfo),
        (status = 404, description = "Model not loaded", body = ErrorResponse)
    ),
    tag = "Model"
)]
pub async fn get_model_info(
    State(state): State<AppState>,
) -> Result<Json<ModelInfo>, (StatusCode, Json<ErrorResponse>)> {
    let engine = state.engine.read().await;

    match engine.get_model_info() {
        Some(info) => Ok(Json(info.clone())),
        None => {
            let error = ErrorResponse {
                error_code: "MODEL_NOT_LOADED".to_string(),
                message: "No model is currently loaded".to_string(),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::NOT_FOUND, Json(error)))
        }
    }
}

/// 加载模型端点
#[utoipa::path(
    post,
    path = "/model/load",
    request_body = LoadModelRequest,
    responses(
        (status = 200, description = "Model loaded successfully", body = LoadModelResponse),
        (status = 400, description = "Invalid request", body = ErrorResponse),
        (status = 500, description = "Internal server error", body = ErrorResponse)
    ),
    tag = "Model"
)]
pub async fn load_model(
    State(state): State<AppState>,
    Json(request): Json<LoadModelRequest>,
) -> Result<Json<LoadModelResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Loading model: {}", request.model_path);

    let mut engine = state.engine.write().await;

    match engine
        .load_model(&request.model_path, request.model_name)
        .await
    {
        Ok(model_info) => {
            let response = LoadModelResponse {
                success: true,
                message: "Model loaded successfully".to_string(),
                model_info: Some(model_info),
            };
            Ok(Json(response))
        }
        Err(e) => {
            error!("Failed to load model: {}", e);
            let error = ErrorResponse {
                error_code: "MODEL_LOAD_FAILED".to_string(),
                message: format!("Failed to load model: {}", e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error)))
        }
    }
}

/// 卸载模型端点
#[utoipa::path(
    post,
    path = "/model/unload",
    responses(
        (status = 200, description = "Model unloaded successfully"),
        (status = 404, description = "No model loaded", body = ErrorResponse)
    ),
    tag = "Model"
)]
pub async fn unload_model(
    State(state): State<AppState>,
) -> Result<StatusCode, (StatusCode, Json<ErrorResponse>)> {
    let mut engine = state.engine.write().await;

    if !engine.is_loaded() {
        let error = ErrorResponse {
            error_code: "MODEL_NOT_LOADED".to_string(),
            message: "No model is currently loaded".to_string(),
            request_id: Some(uuid::Uuid::new_v4().to_string()),
        };
        return Err((StatusCode::NOT_FOUND, Json(error)));
    }

    engine.unload_model();
    info!("Model unloaded successfully");
    Ok(StatusCode::OK)
}

/// 推理端点
#[utoipa::path(
    post,
    path = "/inference",
    request_body = InferenceRequest,
    responses(
        (status = 200, description = "Inference completed successfully", body = InferenceResponse),
        (status = 400, description = "Invalid request", body = ErrorResponse),
        (status = 404, description = "Model not loaded", body = ErrorResponse),
        (status = 500, description = "Inference failed", body = ErrorResponse)
    ),
    tag = "Inference"
)]
pub async fn inference(
    State(state): State<AppState>,
    Json(request): Json<InferenceRequest>,
) -> Result<Json<InferenceResponse>, (StatusCode, Json<ErrorResponse>)> {
    let mut engine = state.engine.write().await;

    if !engine.is_loaded() {
        let error = ErrorResponse {
            error_code: "MODEL_NOT_LOADED".to_string(),
            message: "No model is currently loaded. Please load a model first.".to_string(),
            request_id: Some(uuid::Uuid::new_v4().to_string()),
        };
        return Err((StatusCode::NOT_FOUND, Json(error)));
    }

    // 验证输入数据
    for (name, tensor_data) in &request.inputs {
        if let Err(e) = tensor_data.validate() {
            let error = ErrorResponse {
                error_code: "INVALID_INPUT_DATA".to_string(),
                message: format!("Invalid input data for '{}': {}", name, e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            return Err((StatusCode::BAD_REQUEST, Json(error)));
        }
    }

    match engine.inference(request).await {
        Ok(response) => {
            info!("Inference completed in {:.2}ms", response.inference_time_ms);
            Ok(Json(response))
        }
        Err(e) => {
            error!("Inference failed: {}", e);
            let error = ErrorResponse {
                error_code: "INFERENCE_FAILED".to_string(),
                message: format!("Inference failed: {}", e),
                request_id: Some(uuid::Uuid::new_v4().to_string()),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error)))
        }
    }
}
